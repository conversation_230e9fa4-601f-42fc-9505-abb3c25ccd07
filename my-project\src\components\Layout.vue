<template>
  <div class="layout">
    <Header />
    <main class="main-content">
      <div class="content-container">
        <router-view />
      </div>
    </main>
    <Footer />
  </div>
</template>

<script setup lang="ts">
import Header from './Header.vue'
import Footer from './Footer.vue'
</script>

<style scoped>
.layout {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

.content-container {
  width: 100%;
  max-width: 1280px;
  padding: 0 20px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .content-container {
    max-width: 375px;
    padding: 0 15px;
  }
  
  .main-content {
    padding: 15px 0;
  }
}
</style>
