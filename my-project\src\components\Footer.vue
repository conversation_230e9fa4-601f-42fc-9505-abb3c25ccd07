<template>
  <footer class="footer">
    <div class="footer-container">
      <div class="footer-content">
        <div class="footer-section">
          <h3 class="footer-title">关于我们</h3>
          <p class="footer-text">基于 Vue 3 + TypeScript + Vite 构建的现代化网站</p>
        </div>
        
        <div class="footer-section">
          <h3 class="footer-title">快速链接</h3>
          <ul class="footer-links">
            <li><a href="/" class="footer-link">主页</a></li>
            <li><a href="/circle" class="footer-link">圈子</a></li>
            <li><a href="/tools" class="footer-link">工具包</a></li>
            <li><a href="/tutorial" class="footer-link">教程</a></li>
          </ul>
        </div>
        
        <div class="footer-section">
          <h3 class="footer-title">联系我们</h3>
          <ul class="footer-links">
            <li><a href="#" class="footer-link">意见反馈</a></li>
            <li><a href="#" class="footer-link">商务合作</a></li>
            <li><a href="#" class="footer-link">帮助中心</a></li>
          </ul>
        </div>
      </div>
      
      <div class="footer-bottom">
        <p class="copyright">&copy; 2025 我的网站. 保留所有权利.</p>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
// Footer 组件
</script>

<style scoped>
.footer {
  background-color: #1f2937;
  color: #d1d5db;
  margin-top: auto;
}

.footer-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 40px 20px 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 32px;
  margin-bottom: 32px;
}

.footer-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.footer-title {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin: 0;
}

.footer-text {
  font-size: 14px;
  line-height: 1.6;
  color: #9ca3af;
  margin: 0;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.footer-link {
  color: #9ca3af;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: #3b82f6;
}

.footer-bottom {
  border-top: 1px solid #374151;
  padding-top: 20px;
  text-align: center;
}

.copyright {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .footer-container {
    max-width: 375px;
    padding: 30px 15px 15px;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: 24px;
    margin-bottom: 24px;
  }
  
  .footer-section {
    gap: 12px;
  }
  
  .footer-title {
    font-size: 16px;
  }
  
  .footer-text,
  .footer-link,
  .copyright {
    font-size: 13px;
  }
}
</style>
