# 知识乐园 API 接口总览表

## 接口统计
- **总接口数量**: 42个
- **模块数量**: 12个
- **需要认证的接口**: 28个
- **公开接口**: 14个

## 接口分类总览

| 序号 | 接口名称 | 请求方法 | 接口路径 | 是否需要登录 | 功能描述 |
|------|----------|----------|----------|--------------|----------|
| **1. 用户认证模块** |
| 1 | 用户注册 | POST | `/api/auth/register` | ❌ | 用户注册账号 |
| 2 | 用户登录 | POST | `/api/auth/login` | ❌ | 用户登录系统 |
| 3 | 用户登出 | POST | `/api/auth/logout` | ✅ | 用户退出登录 |
| 4 | 获取用户信息 | GET | `/api/auth/profile` | ✅ | 获取当前登录用户信息 |
| **2. 首页数据模块** |
| 5 | 获取首页横幅数据 | GET | `/api/home/<USER>
| 6 | 获取圈子分类列表 | GET | `/api/categories` | ❌ | 获取所有圈子分类 |
| **3. 圈子模块** |
| 7 | 获取热门圈子列表 | GET | `/api/circles/hot` | ❌ | 获取首页热门圈子列表 |
| 8 | 获取圈子榜单 | GET | `/api/circles/ranking` | ❌ | 获取圈子排行榜 |
| 9 | 获取圈子详情 | GET | `/api/circles/{circleId}` | ❌ | 获取指定圈子的详细信息 |
| 10 | 创建圈子 | POST | `/api/circles` | ✅ | 创建新的圈子 |
| 11 | 加入圈子 | POST | `/api/circles/{circleId}/join` | ✅ | 用户加入指定圈子 |
| 12 | 退出圈子 | DELETE | `/api/circles/{circleId}/leave` | ✅ | 用户退出指定圈子 |
| 13 | 点赞/取消点赞圈子 | POST | `/api/circles/{circleId}/like` | ✅ | 对圈子进行点赞或取消点赞 |
| 14 | 收藏/取消收藏圈子 | POST | `/api/circles/{circleId}/bookmark` | ✅ | 收藏或取消收藏圈子 |
| 15 | 获取圈子成员列表 | GET | `/api/circles/{circleId}/members` | ❌ | 获取圈子成员列表 |
| **4. 话题模块** |
| 16 | 获取热门话题列表 | GET | `/api/topics/hot` | ❌ | 获取热门话题列表 |
| 17 | 创建话题 | POST | `/api/topics` | ✅ | 在圈子中创建新话题 |
| 18 | 获取话题详情 | GET | `/api/topics/{topicId}` | ❌ | 获取话题详细信息 |
| 19 | 点赞话题 | POST | `/api/topics/{topicId}/like` | ✅ | 对话题进行点赞 |
| 20 | 获取话题评论列表 | GET | `/api/topics/{topicId}/comments` | ❌ | 获取话题的评论列表 |
| 21 | 添加话题评论 | POST | `/api/topics/{topicId}/comments` | ✅ | 为话题添加评论 |
| **5. 搜索模块** |
| 22 | 全局搜索 | GET | `/api/search` | ❌ | 全局搜索圈子、话题、用户 |
| 23 | 搜索建议 | GET | `/api/search/suggestions` | ❌ | 获取搜索建议 |
| **6. 用户收藏模块** |
| 24 | 获取我的收藏列表 | GET | `/api/user/bookmarks` | ✅ | 获取用户收藏的圈子和话题 |
| 25 | 管理收藏 | DELETE | `/api/user/bookmarks/{id}` | ✅ | 删除收藏项 |
| **7. 文件上传模块** |
| 26 | 上传图片 | POST | `/api/upload/image` | ✅ | 上传图片文件 |
| 27 | 上传附件 | POST | `/api/upload/file` | ✅ | 上传附件文件 |
| **8. 用户关注模块** |
| 28 | 关注用户 | POST | `/api/users/{userId}/follow` | ✅ | 关注指定用户 |
| 29 | 取消关注 | DELETE | `/api/users/{userId}/unfollow` | ✅ | 取消关注指定用户 |
| 30 | 获取关注列表 | GET | `/api/users/{userId}/following` | ❌ | 获取用户关注的人列表 |
| 31 | 获取粉丝列表 | GET | `/api/users/{userId}/followers` | ❌ | 获取用户的粉丝列表 |
| **9. 通知模块** |
| 32 | 获取通知列表 | GET | `/api/notifications` | ✅ | 获取用户通知列表 |
| 33 | 标记通知已读 | PUT | `/api/notifications/{id}/read` | ✅ | 标记指定通知为已读 |
| 34 | 标记所有通知已读 | PUT | `/api/notifications/read-all` | ✅ | 标记所有通知为已读 |
| **10. 统计分析模块** |
| 35 | 获取首页统计数据 | GET | `/api/stats/home` | ❌ | 获取首页展示的统计数据 |
| 36 | 获取圈子统计 | GET | `/api/stats/circles/{circleId}` | ❌ | 获取指定圈子的统计数据 |
| **11. 内容审核模块** |
| 37 | 举报内容 | POST | `/api/reports` | ✅ | 举报不当内容 |
| **12. 系统配置模块** |
| 38 | 获取系统配置 | GET | `/api/config` | ❌ | 获取系统基础配置信息 |

## 页面功能与接口映射

### 首页 (Home.vue)
| 页面功能 | 对应接口 | 接口路径 |
|----------|----------|----------|
| 顶部横幅显示 | 获取首页横幅数据 | `GET /api/home/<USER>
| 分类导航 | 获取圈子分类列表 | `GET /api/categories` |
| 热门圈子展示 | 获取热门圈子列表 | `GET /api/circles/hot` |
| 圈子点赞功能 | 点赞/取消点赞圈子 | `POST /api/circles/{circleId}/like` |
| 圈子收藏功能 | 收藏/取消收藏圈子 | `POST /api/circles/{circleId}/bookmark` |
| 圈子榜单显示 | 获取圈子榜单 | `GET /api/circles/ranking` |
| 我的收藏显示 | 获取我的收藏列表 | `GET /api/user/bookmarks` |
| 热门话题显示 | 获取热门话题列表 | `GET /api/topics/hot` |

### 头部导航 (Header.vue)
| 页面功能 | 对应接口 | 接口路径 |
|----------|----------|----------|
| 用户登录 | 用户登录 | `POST /api/auth/login` |
| 搜索功能 | 全局搜索 | `GET /api/search` |
| 搜索建议 | 搜索建议 | `GET /api/search/suggestions` |
| 发布内容 | 创建话题 | `POST /api/topics` |
| 用户信息 | 获取用户信息 | `GET /api/auth/profile` |

## 接口优先级分类

### 🔴 高优先级（核心功能）
1. 用户注册/登录接口
2. 获取热门圈子列表
3. 获取圈子榜单
4. 获取热门话题列表
5. 获取首页横幅数据
6. 获取圈子分类列表

### 🟡 中优先级（重要功能）
1. 圈子详情接口
2. 加入/退出圈子
3. 点赞/收藏功能
4. 创建圈子/话题
5. 搜索功能
6. 文件上传

### 🟢 低优先级（辅助功能）
1. 用户关注功能
2. 通知系统
3. 统计分析
4. 内容审核
5. 系统配置

## 开发建议

### 第一阶段：基础功能
- 实现用户认证模块
- 实现首页数据展示
- 实现基础的圈子和话题功能

### 第二阶段：交互功能
- 实现点赞、收藏、关注功能
- 实现搜索功能
- 实现文件上传功能

### 第三阶段：完善功能
- 实现通知系统
- 实现统计分析
- 实现内容审核功能

### 接口测试建议
1. 使用Postman或类似工具进行接口测试
2. 编写单元测试覆盖核心接口
3. 进行压力测试确保性能
4. 实现接口文档自动生成（如Swagger）

### 安全考虑
1. 所有需要登录的接口都要验证token
2. 实现接口限流防止恶意请求
3. 对用户输入进行严格验证
4. 敏感操作需要二次确认
5. 实现CORS跨域安全策略
