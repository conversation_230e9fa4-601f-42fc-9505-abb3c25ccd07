2.1.35 / 2022-03-12
===================

  * deps: mime-db@1.52.0
    - Add extensions from IANA for more `image/*` types
    - Add extension `.asc` to `application/pgp-keys`
    - Add extensions to various XML types
    - Add new upstream MIME types

2.1.34 / 2021-11-08
===================

  * deps: mime-db@1.51.0
    - Add new upstream MIME types

2.1.33 / 2021-10-01
===================

  * deps: mime-db@1.50.0
    - Add deprecated iWorks mime types and extensions
    - Add new upstream MIME types

2.1.32 / 2021-07-27
===================

  * deps: mime-db@1.49.0
    - Add extension `.trig` to `application/trig`
    - Add new upstream MIME types

2.1.31 / 2021-06-01
===================

  * deps: mime-db@1.48.0
    - Add extension `.mvt` to `application/vnd.mapbox-vector-tile`
    - Add new upstream MIME types

2.1.30 / 2021-04-02
===================

  * deps: mime-db@1.47.0
    - Add extension `.amr` to `audio/amr`
    - Remove ambigious extensions from IANA for `application/*+xml` types
    - Update primary extension to `.es` for `application/ecmascript`

2.1.29 / 2021-02-17
===================

  * deps: mime-db@1.46.0
    - Add extension `.amr` to `audio/amr`
    - Add extension `.m4s` to `video/iso.segment`
    - Add extension `.opus` to `audio/ogg`
    - Add new upstream MIME types

2.1.28 / 2021-01-01
===================

  * deps: mime-db@1.45.0
    - Add `application/ubjson` with extension `.ubj`
    - Add `image/avif` with extension `.avif`
    - Add `image/ktx2` with extension `.ktx2`
    - Add extension `.dbf` to `application/vnd.dbf`
    - Add extension `.rar` to `application/vnd.rar`
    - Add extension `.td` to `application/urc-targetdesc+xml`
    - Add new upstream MIME types
    - Fix extension of `application/vnd.apple.keynote` to be `.key`

2.1.27 / 2020-04-23
===================

  * deps: mime-db@1.44.0
    - Add charsets from IANA
    - Add extension `.cjs` to `application/node`
    - Add new upstream MIME types

2.1.26 / 2020-01-05
===================

  * deps: mime-db@1.43.0
    - Add `application/x-keepass2` with extension `.kdbx`
    - Add extension `.mxmf` to `audio/mobile-xmf`
    - Add extensions from IANA for `application/*+xml` types
    - Add new upstream MIME types

2.1.25 / 2019-11-12
===================

  * deps: mime-db@1.42.0
    - Add new upstream MIME types
    - Add `application/toml` with extension `.toml`
    - Add `image/vnd.ms-dds` with extension `.dds`

2.1.24 / 2019-04-20
===================

  * deps: mime-db@1.40.0
    - Add extensions from IANA for `model/*` types
    - Add `text/mdx` with extension `.mdx`

2.1.23 / 2019-04-17
===================

  * deps: mime-db@~1.39.0
    - Add extensions `.siv` and `.sieve` to `application/sieve`
    - Add new upstream MIME types

2.1.22 / 2019-02-14
===================

  * deps: mime-db@~1.38.0
    - Add extension `.nq` to `application/n-quads`
    - Add extension `.nt` to `application/n-triples`
    - Add new upstream MIME types

2.1.21 / 2018-10-19
===================

  * deps: mime-db@~1.37.0
    - Add extensions to HEIC image types
    - Add new upstream MIME types

2.1.20 / 2018-08-26
===================

  * deps: mime-db@~1.36.0
    - Add Apple file extensions from IANA
    - Add extensions from IANA for `image/*` types
    - Add new upstream MIME types

2.1.19 / 2018-07-17
===================

  * deps: mime-db@~1.35.0
    - Add extension `.csl` to `application/vnd.citationstyles.style+xml`
    - Add extension `.es` to `application/ecmascript`
    - Add extension `.owl` to `application/rdf+xml`
    - Add new upstream MIME types
    - Add UTF-8 as default charset for `text/turtle`

2.1.18 / 2018-02-16
===================

  * deps: mime-db@~1.33.0
    - Add `application/raml+yaml` with extension `.raml`
    - Add `application/wasm` with extension `.wasm`
    - Add `text/shex` with extension `.shex`
    - Add extensions for JPEG-2000 images
    - Add extensions from IANA for `message/*` types
    - Add new upstream MIME types
    - Update font MIME types
    - Update `text/hjson` to registered `application/hjson`

2.1.17 / 2017-09-01
===================

  * deps: mime-db@~1.30.0
    - Add `application/vnd.ms-outlook`
    - Add `application/x-arj`
    - Add extension `.mjs` to `application/javascript`
    - Add glTF types and extensions
    - Add new upstream MIME types
    - Add `text/x-org`
    - Add VirtualBox MIME types
    - Fix `source` records for `video/*` types that are IANA
    - Update `font/opentype` to registered `font/otf`

2.1.16 / 2017-07-24
===================

  * deps: mime-db@~1.29.0
    - Add `application/fido.trusted-apps+json`
    - Add extension `.wadl` to `application/vnd.sun.wadl+xml`
    - Add extension `.gz` to `application/gzip`
    - Add new upstream MIME types
    - Update extensions `.md` and `.markdown` to be `text/markdown`

2.1.15 / 2017-03-23
===================

  * deps: mime-db@~1.27.0
    - Add new mime types
    - Add `image/apng`

2.1.14 / 2017-01-14
===================

  * deps: mime-db@~1.26.0
    - Add new mime types

2.1.13 / 2016-11-18
===================

  * deps: mime-db@~1.25.0
    - Add new mime types

2.1.12 / 2016-09-18
===================

  * deps: mime-db@~1.24.0
    - Add new mime types
    - Add `audio/mp3`

2.1.11 / 2016-05-01
===================

  * deps: mime-db@~1.23.0
    - Add new mime types

2.1.10 / 2016-02-15
===================

  * deps: mime-db@~1.22.0
    - Add new mime types
    - Fix extension of `application/dash+xml`
    - Update primary extension for `audio/mp4`

2.1.9 / 2016-01-06
==================

  * deps: mime-db@~1.21.0
    - Add new mime types

2.1.8 / 2015-11-30
==================

  * deps: mime-db@~1.20.0
    - Add new mime types

2.1.7 / 2015-09-20
==================

  * deps: mime-db@~1.19.0
    - Add new mime types

2.1.6 / 2015-09-03
==================

  * deps: mime-db@~1.18.0
    - Add new mime types

2.1.5 / 2015-08-20
==================

  * deps: mime-db@~1.17.0
    - Add new mime types

2.1.4 / 2015-07-30
==================

  * deps: mime-db@~1.16.0
    - Add new mime types

2.1.3 / 2015-07-13
==================

  * deps: mime-db@~1.15.0
    - Add new mime types

2.1.2 / 2015-06-25
==================

  * deps: mime-db@~1.14.0
    - Add new mime types

2.1.1 / 2015-06-08
==================

  * perf: fix deopt during mapping

2.1.0 / 2015-06-07
==================

  * Fix incorrectly treating extension-less file name as extension
    - i.e. `'path/to/json'` will no longer return `application/json`
  * Fix `.charset(type)` to accept parameters
  * Fix `.charset(type)` to match case-insensitive
  * Improve generation of extension to MIME mapping
  * Refactor internals for readability and no argument reassignment
  * Prefer `application/*` MIME types from the same source
  * Prefer any type over `application/octet-stream`
  * deps: mime-db@~1.13.0
    - Add nginx as a source
    - Add new mime types

2.0.14 / 2015-06-06
===================

  * deps: mime-db@~1.12.0
    - Add new mime types

2.0.13 / 2015-05-31
===================

  * deps: mime-db@~1.11.0
    - Add new mime types

2.0.12 / 2015-05-19
===================

  * deps: mime-db@~1.10.0
    - Add new mime types

2.0.11 / 2015-05-05
===================

  * deps: mime-db@~1.9.1
    - Add new mime types

2.0.10 / 2015-03-13
===================

  * deps: mime-db@~1.8.0
    - Add new mime types

2.0.9 / 2015-02-09
==================

  * deps: mime-db@~1.7.0
    - Add new mime types
    - Community extensions ownership transferred from `node-mime`

2.0.8 / 2015-01-29
==================

  * deps: mime-db@~1.6.0
    - Add new mime types

2.0.7 / 2014-12-30
==================

  * deps: mime-db@~1.5.0
    - Add new mime types
    - Fix various invalid MIME type entries

2.0.6 / 2014-12-30
==================

  * deps: mime-db@~1.4.0
    - Add new mime types
    - Fix various invalid MIME type entries
    - Remove example template MIME types

2.0.5 / 2014-12-29
==================

  * deps: mime-db@~1.3.1
    - Fix missing extensions

2.0.4 / 2014-12-10
==================

  * deps: mime-db@~1.3.0
    - Add new mime types

2.0.3 / 2014-11-09
==================

  * deps: mime-db@~1.2.0
    - Add new mime types

2.0.2 / 2014-09-28
==================

  * deps: mime-db@~1.1.0
    - Add new mime types
    - Update charsets

2.0.1 / 2014-09-07
==================

  * Support Node.js 0.6

2.0.0 / 2014-09-02
==================

  * Use `mime-db`
  * Remove `.define()`

1.0.2 / 2014-08-04
==================

  * Set charset=utf-8 for `text/javascript`

1.0.1 / 2014-06-24
==================

  * Add `text/jsx` type

1.0.0 / 2014-05-12
==================

  * Return `false` for unknown types
  * Set charset=utf-8 for `application/json`

0.1.0 / 2014-05-02
==================

  * Initial release
