# 我的网站项目

一个基于现代前端技术栈构建的网站项目。

## 技术栈

- **Node.js** 20+
- **Vue 3** - 渐进式 JavaScript 框架
- **TypeScript** - 类型安全的 JavaScript 超集
- **Vite** - 快速的前端构建工具
- **Vue Router 4** - Vue.js 官方路由管理器

## 项目特性

- ✅ 完整的 TypeScript 支持
- ✅ 基于 Vue 3 Composition API
- ✅ 响应式设计（PC 端优化）
- ✅ 路由管理（首页 + 404 页面）
- ✅ 现代化的项目结构
- ✅ 快速的开发体验

## 快速开始

### 安装依赖

```bash
npm install
```

### 启动开发服务器

```bash
npm run dev
```

项目将在 http://localhost:8000 启动

### 构建生产版本

```bash
npm run build
```

### 预览生产构建

```bash
npm run preview
```

## 项目结构

```
my-project/
├── public/                 # 静态资源
├── src/
│   ├── views/             # 页面组件
│   │   ├── Home.vue       # 首页
│   │   └── NotFound.vue   # 404页面
│   ├── router/            # 路由配置
│   │   └── index.ts       # 路由定义
│   ├── App.vue            # 根组件
│   ├── main.ts            # 应用入口
│   ├── style.css          # 全局样式
│   └── vite-env.d.ts      # TypeScript 声明
├── index.html             # HTML 模板
├── package.json           # 项目配置
├── tsconfig.json          # TypeScript 配置
├── vite.config.ts         # Vite 配置
└── README.md              # 项目说明
```

## 开发说明

- 默认端口：8000
- 支持热重载
- 完整的 TypeScript 类型检查
- 现代化的 ES 模块支持

## 许可证

MIT License
