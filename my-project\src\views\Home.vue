<template>
  <div class="home">
    <!-- 顶部横幅 -->
    <div class="top-banner">
      <div class="banner-content">
        <div class="banner-tag">热门</div>
        <h2 class="banner-title">知识乐园的全新体验学习方式</h2>
        <p class="banner-subtitle">3,222人正在学习</p>
      </div>
    </div>

    <!-- 分类导航 -->
    <div class="category-nav">
      <div class="category-title">圈子分类</div>
      <div class="category-tabs">
        <button class="category-tab active">全部</button>
        <button class="category-tab">前端</button>
        <button class="category-tab">后端</button>
        <button class="category-tab">设计</button>
        <button class="category-tab">运营</button>
        <button class="category-tab">产品</button>
        <button class="category-tab">其他</button>
      </div>
    </div>

    <!-- 热门圈子 -->
    <div class="hot-circles">
      <div class="section-header">
        <h3 class="section-title">热门圈子</h3>
        <a href="#" class="more-link">更多圈子 ></a>
      </div>
      <div class="circles-grid">
        <div class="circle-card" v-for="circle in hotCircles" :key="circle.id">
          <div class="circle-image">
            <img :src="circle.image" :alt="circle.name" />
            <div class="circle-overlay">
              <div class="circle-actions">
                <button class="action-btn like-btn">
                  <span class="icon">❤️</span>
                </button>
                <button class="action-btn bookmark-btn">
                  <span class="icon">🔖</span>
                </button>
              </div>
            </div>
            <span class="new-badge" v-if="circle.isNew">NEW</span>
          </div>
          <div class="circle-info">
            <h4 class="circle-name">{{ circle.name }}</h4>
            <p class="circle-desc">{{ circle.description }}</p>
            <div class="circle-stats">
              <span class="stat-item">
                <span class="stat-icon">👥</span>
                {{ circle.members }}
              </span>
              <span class="stat-item">
                <span class="stat-icon">❤️</span>
                {{ circle.likes }}
              </span>
            </div>
            <div class="circle-tags">
              <span class="tag" v-for="tag in circle.tags" :key="tag">{{ tag }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部内容区 -->
    <div class="bottom-section">
      <!-- 左侧：圈子榜单 -->
      <div class="left-content">
        <div class="ranking-section">
          <h3 class="section-title">圈子榜单</h3>
          <div class="ranking-list">
            <div class="ranking-item" v-for="(item, index) in rankingList" :key="item.id">
              <span class="ranking-number" :class="{ 'top-three': index < 3 }">{{ index + 1 }}</span>
              <div class="ranking-avatar">
                <img :src="item.avatar" :alt="item.name" />
              </div>
              <div class="ranking-info">
                <h4 class="ranking-name">{{ item.name }}</h4>
                <p class="ranking-desc">{{ item.description }}</p>
              </div>
              <span class="ranking-score">{{ item.score }}人</span>
            </div>
          </div>
        </div>

        <!-- 我的收藏 -->
        <div class="my-collection">
          <div class="collection-header">
            <h3 class="section-title">我的收藏</h3>
            <a href="#" class="more-link">管理收藏</a>
          </div>
          <div class="collection-empty">
            <div class="empty-icon">📁</div>
            <p class="empty-text">还没有收藏任何内容</p>
            <button class="explore-btn">去探索吧</button>
          </div>
        </div>

        <!-- 热门话题 -->
        <div class="hot-topics">
          <h3 class="section-title">热门话题</h3>
          <div class="topics-list">
            <div class="topic-item" v-for="topic in hotTopics" :key="topic.id">
              <div class="topic-icon">
                <span class="topic-emoji">{{ topic.emoji }}</span>
              </div>
              <div class="topic-info">
                <h4 class="topic-title">{{ topic.title }}</h4>
                <div class="topic-stats">
                  <span>{{ topic.views }}人 {{ topic.time }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：热门话题详情 -->
      <div class="right-content">
        <div class="hot-topics-detail">
          <h3 class="section-title">热门话题</h3>
          <div class="topics-detail-list">
            <div class="topic-detail-item" v-for="topic in detailTopics" :key="topic.id">
              <div class="topic-detail-icon">
                <span class="topic-detail-emoji">{{ topic.emoji }}</span>
              </div>
              <div class="topic-detail-info">
                <h4 class="topic-detail-title">{{ topic.title }}</h4>
                <div class="topic-detail-stats">
                  <span>{{ topic.views }}人 {{ topic.time }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 热门圈子数据
const hotCircles = ref([
  {
    id: 1,
    name: '人工智能研究',
    description: '探索AI技术的最新发展和应用',
    image: 'https://picsum.photos/300/200?random=1',
    members: '2.5k',
    likes: '1.2k',
    isNew: true,
    tags: ['AI', '机器学习']
  },
  {
    id: 2,
    name: '前端技术交流',
    description: '分享前端开发经验和技巧',
    image: 'https://picsum.photos/300/200?random=2',
    members: '3.7k',
    likes: '2.3k',
    isNew: false,
    tags: ['前端', 'JavaScript']
  },
  {
    id: 3,
    name: '设计分享',
    description: 'UI/UX设计作品展示和交流',
    image: 'https://picsum.photos/300/200?random=3',
    members: '1.8k',
    likes: '956',
    isNew: true,
    tags: ['设计', 'UI']
  },
  {
    id: 4,
    name: '读书',
    description: '分享读书心得和好书推荐',
    image: 'https://picsum.photos/300/200?random=4',
    members: '4.2k',
    likes: '3.1k',
    isNew: false,
    tags: ['读书', '分享']
  },
  {
    id: 5,
    name: '摄影研究',
    description: '摄影技巧分享和作品展示',
    image: 'https://picsum.photos/300/200?random=5',
    members: '2.9k',
    likes: '1.8k',
    isNew: false,
    tags: ['摄影', '艺术']
  },
  {
    id: 6,
    name: '金融投资',
    description: '投资理财知识分享',
    image: 'https://picsum.photos/300/200?random=6',
    members: '3.4k',
    likes: '2.7k',
    isNew: true,
    tags: ['投资', '理财']
  },
  {
    id: 7,
    name: '健身生活',
    description: '健身经验和健康生活方式',
    image: 'https://picsum.photos/300/200?random=7',
    members: '5.1k',
    likes: '4.2k',
    isNew: false,
    tags: ['健身', '健康']
  },
  {
    id: 8,
    name: '美食探索',
    description: '美食制作和餐厅推荐',
    image: 'https://picsum.photos/300/200?random=8',
    members: '6.3k',
    likes: '5.8k',
    isNew: true,
    tags: ['美食', '烹饪']
  }
])

// 圈子榜单数据
const rankingList = ref([
  {
    id: 1,
    name: '人工智能研究',
    description: '1.2k 成员 · 4天前',
    avatar: 'https://picsum.photos/40/40?random=11',
    score: '2.5k'
  },
  {
    id: 2,
    name: '摄影研究',
    description: '956 成员 · 6天前',
    avatar: 'https://picsum.photos/40/40?random=12',
    score: '1.8k'
  },
  {
    id: 3,
    name: '文学创作',
    description: '1.5k 成员 · 1周前',
    avatar: 'https://picsum.photos/40/40?random=13',
    score: '3.2k'
  },
  {
    id: 4,
    name: '前端技术交流',
    description: '2.3k 成员 · 2周前',
    avatar: 'https://picsum.photos/40/40?random=14',
    score: '4.1k'
  },
  {
    id: 5,
    name: '设计学院',
    description: '1.8k 成员 · 3周前',
    avatar: 'https://picsum.photos/40/40?random=15',
    score: '2.8k'
  }
])

// 热门话题数据
const hotTopics = ref([
  {
    id: 1,
    title: '2024年前端技术趋势分析和预测',
    views: '1.2k',
    time: '2小时前',
    emoji: '💻'
  },
  {
    id: 2,
    title: '如何提高代码质量和开发效率',
    views: '856',
    time: '4小时前',
    emoji: '🚀'
  },
  {
    id: 3,
    title: '深入理解JavaScript异步编程',
    views: '2.1k',
    time: '6小时前',
    emoji: '⚡'
  },
  {
    id: 4,
    title: '现代CSS布局技巧和最佳实践',
    views: '1.5k',
    time: '8小时前',
    emoji: '🎨'
  }
])

// 详细话题数据
const detailTopics = ref([
  {
    id: 1,
    title: '2024年前端技术趋势分析和预测',
    views: '1.2k',
    time: '2小时前',
    emoji: '💻'
  },
  {
    id: 2,
    title: '如何提高代码质量和开发效率',
    views: '856',
    time: '4小时前',
    emoji: '🚀'
  },
  {
    id: 3,
    title: '深入理解JavaScript异步编程',
    views: '2.1k',
    time: '6小时前',
    emoji: '⚡'
  },
  {
    id: 4,
    title: '现代CSS布局技巧和最佳实践',
    views: '1.5k',
    time: '8小时前',
    emoji: '🎨'
  }
])
</script>

<style scoped>
.home {
  width: 100%;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 顶部横幅 */
.top-banner {
  height: 120px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><radialGradient id="a"><stop offset="0%" stop-color="%23fff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23fff" stop-opacity="0"/></radialGradient></defs><circle cx="20" cy="20" r="2" fill="url(%23a)"/><circle cx="80" cy="80" r="3" fill="url(%23a)"/><circle cx="40" cy="60" r="1" fill="url(%23a)"/></svg>');
  position: relative;
  display: flex;
  align-items: center;
  padding: 0 2rem;
  margin-bottom: 1.5rem;
}

.banner-content {
  color: white;
}

.banner-tag {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  display: inline-block;
  margin-bottom: 0.5rem;
}

.banner-title {
  font-size: 1.8rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
}

.banner-subtitle {
  font-size: 0.9rem;
  opacity: 0.9;
  margin: 0;
}

/* 分类导航 */
.category-nav {
  background: white;
  padding: 1rem 2rem;
  margin-bottom: 1.5rem;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.category-title {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 1rem;
}

.category-tabs {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.category-tab {
  padding: 6px 16px;
  border: none;
  border-radius: 20px;
  background: #f0f0f0;
  color: #666;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-tab.active {
  background: #6366f1;
  color: white;
}

.category-tab:hover {
  background: #e0e0e0;
}

.category-tab.active:hover {
  background: #5855eb;
}

/* 热门圈子 */
.hot-circles {
  margin-bottom: 2rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0 2rem;
}

.section-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.more-link {
  color: #6366f1;
  text-decoration: none;
  font-size: 0.85rem;
}

.circles-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  padding: 0 2rem;
}

.circle-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.circle-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.circle-image {
  position: relative;
  height: 120px;
  overflow: hidden;
}

.circle-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.circle-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.circle-card:hover .circle-overlay {
  opacity: 1;
}

.circle-actions {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: white;
  transform: scale(1.1);
}

.new-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  background: #ef4444;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
}

.circle-info {
  padding: 1rem;
}

.circle-name {
  font-size: 0.95rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.circle-desc {
  color: #666;
  font-size: 0.8rem;
  margin: 0 0 0.75rem 0;
  line-height: 1.4;
}

.circle-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.75rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #999;
  font-size: 0.75rem;
}

.stat-icon {
  font-size: 0.7rem;
}

.circle-tags {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.tag {
  background: #f0f0f0;
  color: #666;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.7rem;
}

/* 底部内容区 */
.bottom-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 1.5rem;
  padding: 0 2rem;
}

.left-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* 圈子榜单 */
.ranking-section {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.ranking-item:hover {
  background-color: #f9fafb;
}

.ranking-number {
  width: 20px;
  height: 20px;
  background: #6366f1;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  flex-shrink: 0;
}

.ranking-number.top-three {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #333;
}

.ranking-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.ranking-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ranking-info {
  flex: 1;
}

.ranking-name {
  font-size: 0.85rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 0.25rem 0;
}

.ranking-desc {
  font-size: 0.75rem;
  color: #666;
  margin: 0;
}

.ranking-score {
  color: #6366f1;
  font-size: 0.8rem;
  font-weight: 500;
}

/* 我的收藏 */
.my-collection {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.collection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.collection-empty {
  text-align: center;
  padding: 2rem 0;
}

.empty-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.empty-text {
  color: #666;
  font-size: 0.85rem;
  margin: 0 0 1rem 0;
}

.explore-btn {
  background: #6366f1;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.explore-btn:hover {
  background: #5855eb;
}

/* 热门话题 */
.hot-topics {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.topics-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.topic-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.topic-item:hover {
  background-color: #f9fafb;
}

.topic-icon {
  width: 28px;
  height: 28px;
  background: #e0e7ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.topic-emoji {
  font-size: 0.9rem;
}

.topic-info {
  flex: 1;
}

.topic-title {
  font-size: 0.8rem;
  font-weight: 500;
  color: #333;
  margin: 0 0 0.25rem 0;
  line-height: 1.4;
}

.topic-stats {
  font-size: 0.7rem;
  color: #666;
}

/* 右侧内容 */
.right-content {
  display: flex;
  flex-direction: column;
}

.hot-topics-detail {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.topics-detail-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.topic-detail-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.topic-detail-item:hover {
  background-color: #f9fafb;
}

.topic-detail-icon {
  width: 32px;
  height: 32px;
  background: #e0e7ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.topic-detail-emoji {
  font-size: 1rem;
}

.topic-detail-info {
  flex: 1;
}

.topic-detail-title {
  font-size: 0.85rem;
  font-weight: 500;
  color: #333;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.topic-detail-stats {
  font-size: 0.75rem;
  color: #666;
}

/* 移动端适配 */
@media (max-width: 1024px) {
  .circles-grid {
    grid-template-columns: repeat(3, 1fr);
  }

  .bottom-section {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .top-banner {
    height: 100px;
    padding: 0 1rem;
  }

  .banner-title {
    font-size: 1.5rem;
  }

  .category-nav {
    padding: 1rem;
  }

  .circles-grid {
    grid-template-columns: repeat(2, 1fr);
    padding: 0 1rem;
  }

  .section-header {
    padding: 0 1rem;
  }

  .bottom-section {
    padding: 0 1rem;
  }
}

@media (max-width: 480px) {
  .circles-grid {
    grid-template-columns: 1fr;
  }
}
</style>
