<template>
  <div class="home">
    <div class="hero">
      <h1>欢迎来到我的网站</h1>
      <p>这是一个基于 Vue 3 + TypeScript + Vite 构建的现代化网站</p>
    </div>
    
    <div class="features">
      <div class="feature-card">
        <h3>Vue 3</h3>
        <p>使用最新的 Vue 3 Composition API</p>
      </div>
      <div class="feature-card">
        <h3>TypeScript</h3>
        <p>完整的 TypeScript 支持，提供更好的开发体验</p>
      </div>
      <div class="feature-card">
        <h3>Vite</h3>
        <p>快速的构建工具，提供极速的开发体验</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 首页组件
</script>

<style scoped>
.home {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.hero {
  text-align: center;
  margin-bottom: 4rem;
}

.hero h1 {
  font-size: 3rem;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.hero p {
  font-size: 1.2rem;
  color: #7f8c8d;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.feature-card {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
  text-align: center;
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-card h3 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.feature-card p {
  color: #7f8c8d;
  line-height: 1.6;
}
</style>
