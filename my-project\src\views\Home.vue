<template>
  <div class="home">
    <div class="hero">
      <h1>欢迎来到我的网站</h1>
      <p>这是一个基于 Vue 3 + TypeScript + Vite 构建的现代化网站</p>
    </div>

    <div class="features">
      <div class="feature-card">
        <h3>Vue 3</h3>
        <p>使用最新的 Vue 3 Composition API</p>
      </div>
      <div class="feature-card">
        <h3>TypeScript</h3>
        <p>完整的 TypeScript 支持，提供更好的开发体验</p>
      </div>
      <div class="feature-card">
        <h3>Vite</h3>
        <p>快速的构建工具，提供极速的开发体验</p>
      </div>
      <div class="feature-card">
        <h3>Element Plus</h3>
        <p>企业级 UI 组件库，提供丰富的组件</p>
      </div>
      <div class="feature-card">
        <h3>Vue Router</h3>
        <p>官方路由管理器，支持嵌套路由</p>
      </div>
      <div class="feature-card">
        <h3>Vuex</h3>
        <p>状态管理模式，集中式存储管理</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 首页组件
</script>

<style scoped>
.home {
  width: 100%;
}

.hero {
  text-align: center;
  margin-bottom: 4rem;
  padding: 3rem 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px;
  margin-bottom: 3rem;
}

.hero h1 {
  font-size: 3rem;
  margin-bottom: 1rem;
  font-weight: 700;
}

.hero p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.feature-card {
  background: #ffffff;
  padding: 2rem;
  border-radius: 12px;
  text-align: center;
  transition: all 0.3s ease;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border-color: #3b82f6;
}

.feature-card h3 {
  color: #1f2937;
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.feature-card p {
  color: #6b7280;
  line-height: 1.6;
  font-size: 0.95rem;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .hero {
    padding: 2rem 1rem;
    margin-bottom: 2rem;
  }

  .hero h1 {
    font-size: 2rem;
  }

  .hero p {
    font-size: 1rem;
  }

  .features {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .feature-card {
    padding: 1.5rem;
  }
}
</style>
