<template>
  <div class="home">
    <!-- 轮播图区域 -->
    <div class="banner-section">
      <div class="banner-carousel">
        <div class="banner-slide active">
          <img src="https://picsum.photos/1200/300?random=1" alt="Banner 1" />
          <div class="banner-overlay">
            <h2>欢迎来到知识乐园</h2>
            <p>这是一个基于 Vue 3 + TypeScript + Vite 构建的现代化网站</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 热门圈子区域 -->
    <div class="hot-circles-section">
      <div class="section-header">
        <h2 class="section-title">热门圈子</h2>
        <a href="#" class="more-link">更多圈子 ></a>
      </div>
      <div class="circles-grid">
        <div class="circle-card" v-for="circle in hotCircles" :key="circle.id">
          <div class="circle-image">
            <img :src="circle.image" :alt="circle.name" />
            <span class="new-badge" v-if="circle.isNew">NEW</span>
          </div>
          <div class="circle-info">
            <h3 class="circle-name">{{ circle.name }}</h3>
            <p class="circle-desc">{{ circle.description }}</p>
            <div class="circle-stats">
              <span class="stat-item">
                <i class="icon-user"></i>
                {{ circle.members }}
              </span>
              <span class="stat-item">
                <i class="icon-heart"></i>
                {{ circle.likes }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部内容区域 -->
    <div class="bottom-content">
      <!-- 圈子榜单 -->
      <div class="ranking-section">
        <div class="section-header">
          <h2 class="section-title">圈子榜单</h2>
        </div>
        <div class="ranking-list">
          <div class="ranking-item" v-for="(item, index) in rankingList" :key="item.id">
            <span class="ranking-number">{{ index + 1 }}</span>
            <div class="ranking-avatar">
              <img :src="item.avatar" :alt="item.name" />
            </div>
            <div class="ranking-info">
              <h4 class="ranking-name">{{ item.name }}</h4>
              <p class="ranking-desc">{{ item.description }}</p>
            </div>
            <span class="ranking-score">{{ item.score }}人</span>
          </div>
        </div>
      </div>

      <!-- 热门话题 -->
      <div class="hot-topics-section">
        <div class="section-header">
          <h2 class="section-title">热门话题</h2>
        </div>
        <div class="topics-list">
          <div class="topic-item" v-for="topic in hotTopics" :key="topic.id">
            <div class="topic-icon">
              <i class="icon-topic"></i>
            </div>
            <div class="topic-info">
              <h4 class="topic-title">{{ topic.title }}</h4>
              <div class="topic-stats">
                <span>{{ topic.views }}人 {{ topic.time }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 热门圈子数据
const hotCircles = ref([
  {
    id: 1,
    name: 'Vue 3 实战',
    description: '使用最新的 Vue 3 Composition API',
    image: 'https://picsum.photos/300/200?random=1',
    members: '2.5k',
    likes: '1.2k',
    isNew: true
  },
  {
    id: 2,
    name: 'TypeScript 进阶',
    description: '完整的 TypeScript 支持，提供更好的开发体验',
    image: 'https://picsum.photos/300/200?random=2',
    members: '1.8k',
    likes: '956',
    isNew: false
  },
  {
    id: 3,
    name: 'Vite 构建工具',
    description: '快速的构建工具，提供极速的开发体验',
    image: 'https://picsum.photos/300/200?random=3',
    members: '3.2k',
    likes: '2.1k',
    isNew: true
  },
  {
    id: 4,
    name: 'Element Plus',
    description: '企业级 UI 组件库，提供丰富的组件',
    image: 'https://picsum.photos/300/200?random=4',
    members: '4.1k',
    likes: '3.5k',
    isNew: false
  }
])

// 圈子榜单数据
const rankingList = ref([
  {
    id: 1,
    name: 'Vue 3 实战',
    description: '前端开发 · 4天前',
    avatar: 'https://picsum.photos/40/40?random=11',
    score: '2.5k'
  },
  {
    id: 2,
    name: 'TypeScript 进阶',
    description: '编程语言 · 6天前',
    avatar: 'https://picsum.photos/40/40?random=12',
    score: '1.8k'
  },
  {
    id: 3,
    name: 'Vite 构建工具',
    description: '构建工具 · 1周前',
    avatar: 'https://picsum.photos/40/40?random=13',
    score: '3.2k'
  },
  {
    id: 4,
    name: 'Element Plus',
    description: 'UI组件 · 2周前',
    avatar: 'https://picsum.photos/40/40?random=14',
    score: '4.1k'
  },
  {
    id: 5,
    name: 'Vue Router',
    description: '路由管理 · 3周前',
    avatar: 'https://picsum.photos/40/40?random=15',
    score: '2.8k'
  }
])

// 热门话题数据
const hotTopics = ref([
  {
    id: 1,
    title: '如何在Vue3中使用Composition API优化性能？',
    views: '1.2k',
    time: '2小时前'
  },
  {
    id: 2,
    title: 'TypeScript在大型项目中的最佳实践',
    views: '856',
    time: '4小时前'
  },
  {
    id: 3,
    title: 'Vite与Webpack的性能对比分析',
    views: '2.1k',
    time: '6小时前'
  },
  {
    id: 4,
    title: 'Element Plus组件库的深度定制方案',
    views: '1.5k',
    time: '8小时前'
  },
  {
    id: 5,
    title: 'Vue Router 4的新特性详解',
    views: '967',
    time: '12小时前'
  }
])
</script>

<style scoped>
.home {
  width: 100%;
}

/* 轮播图区域 */
.banner-section {
  margin-bottom: 2rem;
}

.banner-carousel {
  position: relative;
  height: 300px;
  border-radius: 12px;
  overflow: hidden;
}

.banner-slide {
  position: relative;
  width: 100%;
  height: 100%;
}

.banner-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: white;
  text-align: center;
}

.banner-overlay h2 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.banner-overlay p {
  font-size: 1.2rem;
  opacity: 0.9;
}

/* 通用区域样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0;
}

.more-link {
  color: #3b82f6;
  text-decoration: none;
  font-size: 0.9rem;
  transition: color 0.3s ease;
}

.more-link:hover {
  color: #2563eb;
}

/* 热门圈子区域 */
.hot-circles-section {
  margin-bottom: 3rem;
}

.circles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
}

.circle-card {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.circle-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.circle-image {
  position: relative;
  height: 160px;
  overflow: hidden;
}

.circle-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.new-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  background: #ef4444;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 600;
}

.circle-info {
  padding: 1rem;
}

.circle-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.circle-desc {
  color: #6b7280;
  font-size: 0.9rem;
  margin: 0 0 1rem 0;
  line-height: 1.4;
}

.circle-stats {
  display: flex;
  gap: 1rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #9ca3af;
  font-size: 0.85rem;
}

.stat-item::before {
  content: '👥';
  font-size: 0.8rem;
}

.stat-item:nth-child(2)::before {
  content: '❤️';
}

/* 底部内容区域 */
.bottom-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

/* 圈子榜单 */
.ranking-section {
  background: #ffffff;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.ranking-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.ranking-item:hover {
  background-color: #f9fafb;
}

.ranking-number {
  width: 24px;
  height: 24px;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
  flex-shrink: 0;
}

.ranking-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.ranking-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.ranking-info {
  flex: 1;
}

.ranking-name {
  font-size: 0.95rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 0.25rem 0;
}

.ranking-desc {
  font-size: 0.8rem;
  color: #6b7280;
  margin: 0;
}

.ranking-score {
  color: #3b82f6;
  font-size: 0.85rem;
  font-weight: 500;
}

/* 热门话题 */
.hot-topics-section {
  background: #ffffff;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.topics-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.topic-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.topic-item:hover {
  background-color: #f9fafb;
}

.topic-icon {
  width: 32px;
  height: 32px;
  background: #e0e7ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.topic-icon::before {
  content: '💬';
  font-size: 1rem;
}

.topic-info {
  flex: 1;
}

.topic-title {
  font-size: 0.9rem;
  font-weight: 500;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.topic-stats {
  font-size: 0.8rem;
  color: #6b7280;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .banner-overlay h2 {
    font-size: 1.8rem;
  }

  .banner-overlay p {
    font-size: 1rem;
  }

  .circles-grid {
    grid-template-columns: 1fr;
  }

  .bottom-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .section-title {
    font-size: 1.3rem;
  }
}
</style>
