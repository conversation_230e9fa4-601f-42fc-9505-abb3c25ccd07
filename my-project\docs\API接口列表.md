# 知识乐园 API 接口列表

## 1. 用户认证模块

### 1.1 用户注册
- **接口路径**: `POST /api/auth/register`
- **功能描述**: 用户注册账号
- **请求参数**:
  ```json
  {
    "username": "string",
    "email": "string", 
    "password": "string",
    "confirmPassword": "string"
  }
  ```
- **响应数据**:
  ```json
  {
    "code": 200,
    "message": "注册成功",
    "data": {
      "userId": "string",
      "token": "string"
    }
  }
  ```

### 1.2 用户登录
- **接口路径**: `POST /api/auth/login`
- **功能描述**: 用户登录系统
- **请求参数**:
  ```json
  {
    "email": "string",
    "password": "string"
  }
  ```
- **响应数据**:
  ```json
  {
    "code": 200,
    "message": "登录成功",
    "data": {
      "userId": "string",
      "username": "string",
      "avatar": "string",
      "token": "string"
    }
  }
  ```

### 1.3 用户登出
- **接口路径**: `POST /api/auth/logout`
- **功能描述**: 用户退出登录
- **请求头**: `Authorization: Bearer {token}`

### 1.4 获取用户信息
- **接口路径**: `GET /api/auth/profile`
- **功能描述**: 获取当前登录用户信息
- **请求头**: `Authorization: Bearer {token}`

## 2. 首页数据模块

### 2.1 获取首页横幅数据
- **接口路径**: `GET /api/home/<USER>
- **功能描述**: 获取首页顶部横幅信息
- **响应数据**:
  ```json
  {
    "code": 200,
    "data": {
      "title": "知识乐园的全新体验学习方式",
      "subtitle": "3,222人正在学习",
      "tag": "热门"
    }
  }
  ```

### 2.2 获取圈子分类列表
- **接口路径**: `GET /api/categories`
- **功能描述**: 获取所有圈子分类
- **响应数据**:
  ```json
  {
    "code": 200,
    "data": [
      {
        "id": "string",
        "name": "全部",
        "isDefault": true
      },
      {
        "id": "string", 
        "name": "前端",
        "isDefault": false
      }
    ]
  }
  ```

## 3. 圈子模块

### 3.1 获取热门圈子列表
- **接口路径**: `GET /api/circles/hot`
- **功能描述**: 获取首页热门圈子列表
- **请求参数**:
  ```
  ?page=1&limit=8&category=string
  ```
- **响应数据**:
  ```json
  {
    "code": 200,
    "data": {
      "list": [
        {
          "id": "string",
          "name": "人工智能研究",
          "description": "探索AI技术的最新发展和应用",
          "image": "string",
          "members": "2.5k",
          "likes": "1.2k",
          "isNew": true,
          "tags": ["AI", "机器学习"],
          "category": "技术"
        }
      ],
      "total": 100,
      "page": 1,
      "limit": 8
    }
  }
  ```

### 3.2 获取圈子榜单
- **接口路径**: `GET /api/circles/ranking`
- **功能描述**: 获取圈子排行榜
- **请求参数**:
  ```
  ?type=members&limit=5
  ```
- **响应数据**:
  ```json
  {
    "code": 200,
    "data": [
      {
        "id": "string",
        "name": "人工智能研究",
        "description": "1.2k 成员 · 4天前",
        "avatar": "string",
        "score": "2.5k",
        "rank": 1
      }
    ]
  }
  ```

### 3.3 获取圈子详情
- **接口路径**: `GET /api/circles/{circleId}`
- **功能描述**: 获取指定圈子的详细信息
- **响应数据**:
  ```json
  {
    "code": 200,
    "data": {
      "id": "string",
      "name": "string",
      "description": "string",
      "image": "string",
      "members": "string",
      "likes": "string",
      "tags": ["string"],
      "createdAt": "string",
      "isJoined": false,
      "isLiked": false
    }
  }
  ```

### 3.4 创建圈子
- **接口路径**: `POST /api/circles`
- **功能描述**: 创建新的圈子
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:
  ```json
  {
    "name": "string",
    "description": "string",
    "image": "string",
    "category": "string",
    "tags": ["string"]
  }
  ```

### 3.5 加入圈子
- **接口路径**: `POST /api/circles/{circleId}/join`
- **功能描述**: 用户加入指定圈子
- **请求头**: `Authorization: Bearer {token}`

### 3.6 退出圈子
- **接口路径**: `DELETE /api/circles/{circleId}/leave`
- **功能描述**: 用户退出指定圈子
- **请求头**: `Authorization: Bearer {token}`

### 3.7 点赞/取消点赞圈子
- **接口路径**: `POST /api/circles/{circleId}/like`
- **功能描述**: 对圈子进行点赞或取消点赞
- **请求头**: `Authorization: Bearer {token}`

### 3.8 收藏/取消收藏圈子
- **接口路径**: `POST /api/circles/{circleId}/bookmark`
- **功能描述**: 收藏或取消收藏圈子
- **请求头**: `Authorization: Bearer {token}`

### 3.9 获取圈子成员列表
- **接口路径**: `GET /api/circles/{circleId}/members`
- **功能描述**: 获取圈子成员列表
- **请求参数**:
  ```
  ?page=1&limit=20
  ```

## 4. 话题模块

### 4.1 获取热门话题列表
- **接口路径**: `GET /api/topics/hot`
- **功能描述**: 获取热门话题列表
- **请求参数**:
  ```
  ?page=1&limit=10
  ```
- **响应数据**:
  ```json
  {
    "code": 200,
    "data": [
      {
        "id": "string",
        "title": "2024年前端技术趋势分析和预测",
        "views": "1.2k",
        "time": "2小时前",
        "emoji": "💻",
        "circleId": "string",
        "circleName": "前端技术交流"
      }
    ]
  }
  ```

### 4.2 创建话题
- **接口路径**: `POST /api/topics`
- **功能描述**: 在圈子中创建新话题
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:
  ```json
  {
    "title": "string",
    "content": "string",
    "circleId": "string",
    "images": ["string"],
    "attachments": ["string"]
  }
  ```

### 4.3 获取话题详情
- **接口路径**: `GET /api/topics/{topicId}`
- **功能描述**: 获取话题详细信息

### 4.4 点赞话题
- **接口路径**: `POST /api/topics/{topicId}/like`
- **功能描述**: 对话题进行点赞
- **请求头**: `Authorization: Bearer {token}`

### 4.5 获取话题评论列表
- **接口路径**: `GET /api/topics/{topicId}/comments`
- **功能描述**: 获取话题的评论列表
- **请求参数**:
  ```
  ?page=1&limit=20
  ```

### 4.6 添加话题评论
- **接口路径**: `POST /api/topics/{topicId}/comments`
- **功能描述**: 为话题添加评论
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:
  ```json
  {
    "content": "string",
    "parentId": "string"
  }
  ```

## 5. 搜索模块

### 5.1 全局搜索
- **接口路径**: `GET /api/search`
- **功能描述**: 全局搜索圈子、话题、用户
- **请求参数**:
  ```
  ?q=关键词&type=all&page=1&limit=20
  ```

### 5.2 搜索建议
- **接口路径**: `GET /api/search/suggestions`
- **功能描述**: 获取搜索建议
- **请求参数**:
  ```
  ?q=关键词
  ```

## 6. 用户收藏模块

### 6.1 获取我的收藏列表
- **接口路径**: `GET /api/user/bookmarks`
- **功能描述**: 获取用户收藏的圈子和话题
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:
  ```
  ?type=circles&page=1&limit=20
  ```

### 6.2 管理收藏
- **接口路径**: `DELETE /api/user/bookmarks/{id}`
- **功能描述**: 删除收藏项
- **请求头**: `Authorization: Bearer {token}`

## 7. 文件上传模块

### 7.1 上传图片
- **接口路径**: `POST /api/upload/image`
- **功能描述**: 上传图片文件
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**: `FormData`
- **响应数据**:
  ```json
  {
    "code": 200,
    "data": {
      "url": "string",
      "filename": "string"
    }
  }
  ```

### 7.2 上传附件
- **接口路径**: `POST /api/upload/file`
- **功能描述**: 上传附件文件
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**: `FormData`

## 8. 用户关注模块

### 8.1 关注用户
- **接口路径**: `POST /api/users/{userId}/follow`
- **功能描述**: 关注指定用户
- **请求头**: `Authorization: Bearer {token}`

### 8.2 取消关注
- **接口路径**: `DELETE /api/users/{userId}/unfollow`
- **功能描述**: 取消关注指定用户
- **请求头**: `Authorization: Bearer {token}`

### 8.3 获取关注列表
- **接口路径**: `GET /api/users/{userId}/following`
- **功能描述**: 获取用户关注的人列表

### 8.4 获取粉丝列表
- **接口路径**: `GET /api/users/{userId}/followers`
- **功能描述**: 获取用户的粉丝列表

## 9. 通知模块

### 9.1 获取通知列表
- **接口路径**: `GET /api/notifications`
- **功能描述**: 获取用户通知列表
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:
  ```
  ?page=1&limit=20&type=all
  ```
- **响应数据**:
  ```json
  {
    "code": 200,
    "data": {
      "list": [
        {
          "id": "string",
          "type": "like|comment|follow|join",
          "title": "string",
          "content": "string",
          "isRead": false,
          "createdAt": "string",
          "relatedId": "string"
        }
      ],
      "unreadCount": 5
    }
  }
  ```

### 9.2 标记通知已读
- **接口路径**: `PUT /api/notifications/{id}/read`
- **功能描述**: 标记指定通知为已读
- **请求头**: `Authorization: Bearer {token}`

### 9.3 标记所有通知已读
- **接口路径**: `PUT /api/notifications/read-all`
- **功能描述**: 标记所有通知为已读
- **请求头**: `Authorization: Bearer {token}`

## 10. 统计分析模块

### 10.1 获取首页统计数据
- **接口路径**: `GET /api/stats/home`
- **功能描述**: 获取首页展示的统计数据
- **响应数据**:
  ```json
  {
    "code": 200,
    "data": {
      "totalUsers": 3222,
      "totalCircles": 156,
      "totalTopics": 2845,
      "activeUsers": 892
    }
  }
  ```

### 10.2 获取圈子统计
- **接口路径**: `GET /api/stats/circles/{circleId}`
- **功能描述**: 获取指定圈子的统计数据

## 11. 内容审核模块

### 11.1 举报内容
- **接口路径**: `POST /api/reports`
- **功能描述**: 举报不当内容
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:
  ```json
  {
    "type": "circle|topic|comment|user",
    "targetId": "string",
    "reason": "string",
    "description": "string"
  }
  ```

## 12. 系统配置模块

### 12.1 获取系统配置
- **接口路径**: `GET /api/config`
- **功能描述**: 获取系统基础配置信息
- **响应数据**:
  ```json
  {
    "code": 200,
    "data": {
      "siteName": "知识乐园",
      "siteDescription": "string",
      "maxFileSize": 10485760,
      "allowedFileTypes": ["jpg", "png", "gif", "pdf"],
      "features": {
        "enableRegistration": true,
        "enableUpload": true
      }
    }
  }
  ```

## 接口通用规范

### 请求头规范
```
Content-Type: application/json
Authorization: Bearer {token}  // 需要登录的接口
```

### 响应格式规范
```json
{
  "code": 200,           // 状态码：200成功，400客户端错误，500服务器错误
  "message": "string",   // 响应消息
  "data": {},           // 响应数据
  "timestamp": "string"  // 响应时间戳
}
```

### 错误码定义
- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权（未登录或token过期）
- `403`: 禁止访问（权限不足）
- `404`: 资源不存在
- `409`: 资源冲突（如用户名已存在）
- `422`: 请求参数验证失败
- `429`: 请求频率限制
- `500`: 服务器内部错误

### 分页参数规范
```
?page=1&limit=20&sort=createdAt&order=desc
```

### 排序参数说明
- `sort`: 排序字段（createdAt, updatedAt, likes, members等）
- `order`: 排序方向（asc升序, desc降序）

### 文件上传规范
- 图片文件：支持jpg, png, gif格式，最大10MB
- 附件文件：支持pdf, doc, docx, txt格式，最大50MB
- 上传接口返回文件URL，用于后续引用

### 接口版本控制
- 当前版本：v1
- 接口路径格式：`/api/v1/{module}/{action}`
- 向后兼容原则，新版本不影响旧版本使用

### 接口限流规则
- 登录接口：每分钟最多5次尝试
- 发布内容：每分钟最多10次
- 文件上传：每分钟最多20次
- 其他接口：每分钟最多100次

### 缓存策略
- 静态配置：缓存1小时
- 用户信息：缓存30分钟
- 圈子列表：缓存15分钟
- 话题列表：缓存5分钟
