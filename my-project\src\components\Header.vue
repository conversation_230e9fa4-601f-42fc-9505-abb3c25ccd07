<template>
  <header class="header">
    <div class="header-container">
      <!-- Logo -->
      <div class="logo">
        <img src="https://picsum.photos/40/40" alt="Logo" class="logo-img" />
        <span class="logo-text">我的网站</span>
      </div>
      
      <!-- 导航栏 -->
      <nav class="nav">
        <router-link 
          v-for="item in navItems" 
          :key="item.path"
          :to="item.path" 
          class="nav-item"
          :class="{ active: $route.path === item.path }"
        >
          {{ item.name }}
        </router-link>
      </nav>
      
      <!-- 右侧操作区 -->
      <div class="header-actions">
        <!-- 搜索框 -->
        <div class="search-box">
          <input 
            type="text" 
            placeholder="搜索..." 
            class="search-input"
            v-model="searchQuery"
          />
          <button class="search-btn" @click="handleSearch">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="11" cy="11" r="8"></circle>
              <path d="m21 21-4.35-4.35"></path>
            </svg>
          </button>
        </div>
        
        <!-- 发布按钮 -->
        <button class="publish-btn">发布</button>
        
        <!-- 登录按钮 -->
        <button class="login-btn">登录</button>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const searchQuery = ref('')

const navItems = [
  { name: '主页', path: '/' },
  { name: '圈子', path: '/circle' },
  { name: '工具包', path: '/tools' },
  { name: '教程', path: '/tutorial' },
  { name: '问答', path: '/qa' },
  { name: '求职', path: '/jobs' }
]

const handleSearch = () => {
  if (searchQuery.value.trim()) {
    console.log('搜索:', searchQuery.value)
    // 这里可以添加搜索逻辑
  }
}
</script>

<style scoped>
.header {
  height: 60px;
  background-color: #ffffff;
  border-bottom: 1px solid #e5e7eb;
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header-container {
  max-width: 1320px;
  height: 100%;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

/* Logo 样式 */
.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  text-decoration: none;
  color: #1f2937;
}

.logo-img {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  object-fit: cover;
}

.logo-text {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

/* 导航栏样式 */
.nav {
  display: flex;
  align-items: center;
  gap: 32px;
}

.nav-item {
  position: relative;
  padding: 18px 0;
  color: #6b7280;
  text-decoration: none;
  font-size: 16px;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-item:hover {
  color: #3b82f6;
}

.nav-item.active {
  color: #3b82f6;
}

.nav-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #3b82f6;
}

/* 右侧操作区样式 */
.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 搜索框样式 */
.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 240px;
  height: 36px;
  padding: 0 40px 0 12px;
  border: 1px solid #d1d5db;
  border-radius: 18px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  border-color: #3b82f6;
}

.search-btn {
  position: absolute;
  right: 8px;
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.3s ease;
}

.search-btn:hover {
  color: #3b82f6;
}

/* 按钮样式 */
.publish-btn {
  height: 36px;
  padding: 0 16px;
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.publish-btn:hover {
  background-color: #2563eb;
}

.login-btn {
  height: 36px;
  padding: 0 16px;
  background-color: transparent;
  color: #374151;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.login-btn:hover {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .header-container {
    max-width: 375px;
    padding: 0 15px;
  }
  
  .nav {
    display: none;
  }
  
  .search-input {
    width: 160px;
  }
  
  .header-actions {
    gap: 12px;
  }
  
  .publish-btn,
  .login-btn {
    padding: 0 12px;
    font-size: 13px;
  }
}
</style>
