{"name": "my-project", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --port 8000", "build": "tsc && vite build", "preview": "vite preview"}, "devDependencies": {"typescript": "~5.8.3", "vite": "^7.0.4"}, "dependencies": {"@vitejs/plugin-vue": "^6.0.0", "axios": "^1.10.0", "echarts": "^5.6.0", "element-plus": "^2.10.4", "mitt": "^3.0.1", "vue": "^3.5.17", "vue-router": "^4.5.1", "vuex": "^4.1.0", "vuex-along": "^1.2.13"}}