const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'root',
  password: '123456',
  database: 'ashuo',
  charset: 'utf8mb4',
  timezone: '+08:00'
};

// 创建连接池
const pool = mysql.createPool({
  ...dbConfig,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// 数据库连接测试函数
async function testConnection() {
  try {
    console.log('🔄 正在连接数据库...');
    
    // 测试连接
    const connection = await pool.getConnection();
    console.log('✅ 数据库连接成功！');
    
    // 查询数据库信息
    const [rows] = await connection.execute('SELECT DATABASE() as current_db, VERSION() as mysql_version');
    console.log(`📊 当前数据库: ${rows[0].current_db}`);
    console.log(`🔧 MySQL版本: ${rows[0].mysql_version}`);
    
    // 查询所有表名
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('📋 数据库表列表:');
    if (tables.length === 0) {
      console.log('   暂无数据表');
    } else {
      tables.forEach((table, index) => {
        const tableName = Object.values(table)[0];
        console.log(`   ${index + 1}. ${tableName}`);
      });
    }
    
    // 释放连接
    connection.release();
    
    return {
      success: true,
      database: rows[0].current_db,
      version: rows[0].mysql_version,
      tables: tables.map(table => Object.values(table)[0])
    };
    
  } catch (error) {
    console.error('❌ 数据库连接失败:');
    console.error(`   错误信息: ${error.message}`);
    console.error(`   错误代码: ${error.code || 'UNKNOWN'}`);
    
    // 根据错误类型给出建议
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('   建议: 请检查用户名和密码是否正确');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.error('   建议: 请检查数据库名称是否正确，或创建对应的数据库');
    } else if (error.code === 'ECONNREFUSED') {
      console.error('   建议: 请检查MySQL服务是否已启动');
    }
    
    throw error;
  }
}

// 执行SQL查询的通用函数
async function query(sql, params = []) {
  try {
    const [rows] = await pool.execute(sql, params);
    return rows;
  } catch (error) {
    console.error('SQL查询错误:', error.message);
    throw error;
  }
}

// 获取连接池状态
function getPoolStatus() {
  return {
    totalConnections: pool.pool._allConnections.length,
    freeConnections: pool.pool._freeConnections.length,
    acquiringConnections: pool.pool._acquiringConnections.length
  };
}

module.exports = {
  pool,
  testConnection,
  query,
  getPoolStatus
};
