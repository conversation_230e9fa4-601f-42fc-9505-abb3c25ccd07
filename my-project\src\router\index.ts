import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import Layout from '../components/Layout.vue'
import Home from '../views/Home.vue'
import NotFound from '../views/NotFound.vue'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: Layout,
    children: [
      {
        path: '',
        name: 'Home',
        component: Home
      },
      {
        path: 'circle',
        name: 'Circle',
        component: () => import('../views/Home.vue') // 临时使用 Home 组件
      },
      {
        path: 'tools',
        name: 'Tools',
        component: () => import('../views/Home.vue') // 临时使用 Home 组件
      },
      {
        path: 'tutorial',
        name: 'Tutorial',
        component: () => import('../views/Home.vue') // 临时使用 Home 组件
      },
      {
        path: 'qa',
        name: 'QA',
        component: () => import('../views/Home.vue') // 临时使用 Home 组件
      },
      {
        path: 'jobs',
        name: 'Jobs',
        component: () => import('../views/Home.vue') // 临时使用 Home 组件
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
