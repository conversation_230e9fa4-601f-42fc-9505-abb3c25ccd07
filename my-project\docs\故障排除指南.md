# 故障排除指南

## 常见问题及解决方案

### 1. 页面显示"ERR_EMPTY_RESPONSE"错误

**问题描述**: 浏览器显示"该网页无法正常运作"和"ERR_EMPTY_RESPONSE"错误

**可能原因**:
- 开发服务器未启动或崩溃
- 依赖包缺失或版本冲突
- 端口被占用
- 编译错误导致服务器无法响应

**解决步骤**:

#### 步骤1: 检查开发服务器状态
```bash
# 检查服务器是否在运行
netstat -ano | findstr :8000

# 或者检查进程
tasklist | findstr node
```

#### 步骤2: 重启开发服务器
```bash
# 停止当前服务器 (Ctrl+C)
# 然后重新启动
npm run dev
```

#### 步骤3: 检查依赖包
```bash
# 检查是否有缺失的依赖
npm install

# 检查特定包是否安装
npm list pinia
npm list vue
npm list vue-router
```

#### 步骤4: 清理缓存
```bash
# 清理npm缓存
npm cache clean --force

# 删除node_modules并重新安装
rm -rf node_modules
npm install
```

### 2. Pinia相关错误

**问题描述**: 控制台显示"Failed to resolve import 'pinia'"

**解决方案**:
```bash
# 安装pinia
npm install pinia --save

# 确保在main.ts中正确配置
# import { createPinia } from 'pinia'
# app.use(createPinia())
```

### 3. 路由相关问题

**问题描述**: 页面路由不工作或显示404

**检查项目**:
1. 确保router/index.ts配置正确
2. 检查组件导入路径
3. 确保Layout组件存在
4. 检查路由模式配置

**解决方案**:
```typescript
// 确保路由配置正确
const router = createRouter({
  history: createWebHistory(),
  routes
})
```

### 4. TypeScript编译错误

**问题描述**: TypeScript类型错误导致编译失败

**解决步骤**:
```bash
# 检查TypeScript配置
npx tsc --noEmit

# 检查具体错误
npm run build
```

### 5. 样式加载问题

**问题描述**: CSS样式不生效或加载失败

**检查项目**:
1. 确保style.css文件存在
2. 检查组件中的scoped样式
3. 确保CSS导入路径正确

### 6. 端口占用问题

**问题描述**: 端口8000被占用

**解决方案**:
```bash
# 查找占用端口的进程
netstat -ano | findstr :8000

# 杀死占用进程
taskkill /PID <进程ID> /F

# 或者使用不同端口
npm run dev -- --port 3000
```

## 调试技巧

### 1. 浏览器开发者工具
- 打开F12开发者工具
- 查看Console标签页的错误信息
- 检查Network标签页的网络请求
- 查看Sources标签页的源代码

### 2. 服务器日志
- 查看终端中的Vite输出
- 注意编译错误和警告信息
- 检查HMR（热更新）状态

### 3. 网络测试
```bash
# 测试服务器响应
curl http://localhost:8000

# 或使用PowerShell
Invoke-WebRequest -Uri http://localhost:8000
```

## 预防措施

### 1. 定期更新依赖
```bash
# 检查过时的包
npm outdated

# 更新依赖
npm update
```

### 2. 使用版本控制
- 定期提交代码到Git
- 创建功能分支进行开发
- 使用.gitignore忽略node_modules

### 3. 环境一致性
- 使用package-lock.json锁定依赖版本
- 记录Node.js和npm版本
- 使用Docker容器化开发环境

## 性能优化

### 1. 开发环境优化
```javascript
// vite.config.ts
export default defineConfig({
  server: {
    hmr: true,
    open: true
  },
  build: {
    sourcemap: true
  }
})
```

### 2. 代码分割
```typescript
// 使用动态导入
const Login = () => import('../views/Login.vue')
```

### 3. 缓存策略
- 启用浏览器缓存
- 使用CDN加速静态资源
- 配置适当的缓存头

## 监控和日志

### 1. 错误监控
```typescript
// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Global error:', err, info)
}
```

### 2. 性能监控
```typescript
// 性能指标收集
window.addEventListener('load', () => {
  const perfData = performance.getEntriesByType('navigation')[0]
  console.log('Page load time:', perfData.loadEventEnd - perfData.fetchStart)
})
```

## 联系支持

如果以上解决方案都无法解决问题，请：

1. 收集错误信息和日志
2. 记录重现步骤
3. 检查系统环境信息
4. 提供代码版本信息

## 快速检查清单

当遇到问题时，按以下顺序检查：

- [ ] 开发服务器是否正在运行
- [ ] 是否有编译错误
- [ ] 依赖包是否完整安装
- [ ] 端口是否被占用
- [ ] 浏览器缓存是否需要清理
- [ ] 网络连接是否正常
- [ ] 代码是否有语法错误
- [ ] 路由配置是否正确

## 常用命令

```bash
# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview

# 安装依赖
npm install

# 检查依赖
npm list

# 清理缓存
npm cache clean --force

# 重新安装依赖
rm -rf node_modules package-lock.json
npm install
```

记住：大多数问题都可以通过重启开发服务器和重新安装依赖来解决！
