{"name": "learning-happy-paradise-api", "version": "1.0.0", "description": "后台服务API项目，基于Node.js + Express + MySQL + JWT", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["nodejs", "express", "mysql", "jwt", "api"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.2"}}