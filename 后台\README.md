# 知识乐园后台API服务

基于 Node.js + Express + MySQL + JWT 的后台服务API项目

## 技术栈

- **Node.js 20+** - 运行环境
- **Express** - Web框架
- **MySQL** - 数据库
- **JWT** - 身份验证
- **nodemon** - 开发环境热重载

## 项目结构

```
├── app.js              # 主入口文件
├── package.json        # 项目配置和依赖
├── config/             # 配置文件
│   └── database.js     # 数据库连接配置
├── routes/             # 路由文件
├── middleware/         # 中间件
├── utils/              # 工具函数
└── .env.example        # 环境变量示例
```

## 安装和运行

1. 安装依赖：
```bash
npm install
```

2. 配置环境变量（可选）：
```bash
cp .env.example .env
# 编辑 .env 文件中的配置
```

3. 确保MySQL服务已启动，数据库和表已创建：
```sql
CREATE DATABASE ashuo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
-- users表已自动创建
```

4. 启动项目：
```bash
# 开发环境（热重载）
npm run dev

# 生产环境
npm start
```

## 数据库配置

- **主机**: localhost
- **用户名**: root
- **密码**: 123456
- **数据库名**: ashuo

## API接口

### 系统接口
- `GET /` - 服务状态信息
- `GET /health` - 健康检查

### 用户接口
- `POST /api/users/register` - 用户注册
- `POST /api/users/login` - 用户登录（支持用户名/邮箱登录）
- `GET /api/users/:id` - 获取用户信息

### 圈子接口
- `POST /api/circles/list` - 获取圈子列表（支持分页、筛选、排序）
- `GET /api/circles/:id` - 获取圈子详情

详细的API文档请查看 [API_DOCS.md](./API_DOCS.md)

## 默认端口

项目默认运行在端口 **4001**

访问地址：http://localhost:4001

## 项目特性

- ✅ Express框架搭建
- ✅ MySQL数据库连接配置
- ✅ 用户注册接口（含数据验证）
- ✅ 密码加密（PBKDF2 + 随机盐值）
- ✅ 输入数据验证中间件
- ✅ 错误处理机制
- ✅ CORS跨域支持
- ✅ JWT准备（已安装依赖）
- ✅ 真实数据库操作（已连接ashuo数据库）
- ✅ 用户重复检查（用户名、邮箱、手机号）
- ✅ 圈子列表接口（支持分页、筛选、排序）
- ✅ 圈子详情接口
- ✅ 参数验证中间件（圈子查询参数验证）
- ✅ 用户登录接口（支持用户名/邮箱登录）
- ✅ JWT身份验证（token生成和验证）
- ✅ 密码验证和安全登录

## 测试

### 测试用户注册接口

1. 启动服务器：
```bash
npm run dev
```

2. 发送注册请求：
```bash
curl -X POST http://localhost:4001/api/users/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser001",
    "email": "<EMAIL>",
    "password": "test123456",
    "nickname": "Test User"
  }'
```

### 测试用户登录接口

1. 使用用户名登录：
```bash
curl -X POST http://localhost:4001/api/users/login \
  -H "Content-Type: application/json" \
  -d '{
    "login": "testuser001",
    "password": "test123456"
  }'
```

2. 使用邮箱登录：
```bash
curl -X POST http://localhost:4001/api/users/login \
  -H "Content-Type: application/json" \
  -d '{
    "login": "<EMAIL>",
    "password": "alice123456"
  }'
```

### 测试数据验证

发送无效数据测试验证功能：
```bash
curl -X POST http://localhost:4001/api/users/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "123invalid",
    "email": "invalid-email",
    "password": "123"
  }'
```

### 测试圈子列表接口

1. 获取所有圈子列表：
```bash
curl -X POST http://localhost:4001/api/circles/list \
  -H "Content-Type: application/json" \
  -d '{
    "page": 1,
    "pageSize": 10,
    "status": 1
  }'
```

2. 带筛选条件的查询：
```bash
curl -X POST http://localhost:4001/api/circles/list \
  -H "Content-Type: application/json" \
  -d '{
    "page": 1,
    "pageSize": 5,
    "category_id": 1,
    "type": 1,
    "keyword": "技术",
    "sort_by": "member_count",
    "sort_order": "desc"
  }'
```

3. 获取圈子详情：
```bash
curl http://localhost:4001/api/circles/13
```
