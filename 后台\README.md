# 知识乐园后台API服务

基于 Node.js + Express + MySQL + JWT 的后台服务API项目

## 技术栈

- **Node.js 20+** - 运行环境
- **Express** - Web框架
- **MySQL** - 数据库
- **JWT** - 身份验证
- **nodemon** - 开发环境热重载

## 项目结构

```
├── app.js              # 主入口文件
├── package.json        # 项目配置和依赖
├── config/             # 配置文件
│   └── database.js     # 数据库连接配置
├── routes/             # 路由文件
├── middleware/         # 中间件
├── utils/              # 工具函数
└── .env.example        # 环境变量示例
```

## 安装和运行

1. 安装依赖：
```bash
npm install
```

2. 配置环境变量（可选）：
```bash
cp .env.example .env
# 编辑 .env 文件中的配置
```

3. 确保MySQL服务已启动，并创建数据库：
```sql
CREATE DATABASE learning_happy_paradise CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

4. 启动项目：
```bash
# 开发环境（热重载）
npm run dev

# 生产环境
npm start
```

## 数据库配置

- **主机**: localhost
- **用户名**: root
- **密码**: rootroot
- **数据库名**: learning_happy_paradise

## API接口

### 系统接口
- `GET /` - 服务状态信息
- `GET /health` - 健康检查

### 用户接口
- `POST /api/users/register` - 用户注册
- `GET /api/users/:id` - 获取用户信息

详细的API文档请查看 [API_DOCS.md](./API_DOCS.md)

## 默认端口

项目默认运行在端口 **4001**

访问地址：http://localhost:4001

## 项目特性

- ✅ Express框架搭建
- ✅ MySQL数据库连接配置
- ✅ 用户注册接口（含数据验证）
- ✅ 密码加密（PBKDF2 + 随机盐值）
- ✅ 输入数据验证中间件
- ✅ 错误处理机制
- ✅ CORS跨域支持
- ✅ JWT准备（已安装依赖）

## 测试

### 测试用户注册接口

1. 启动服务器：
```bash
npm run dev
```

2. 发送注册请求：
```bash
curl -X POST http://localhost:4001/api/users/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser001",
    "email": "<EMAIL>",
    "password": "test123456",
    "nickname": "Test User"
  }'
```

### 测试数据验证

发送无效数据测试验证功能：
```bash
curl -X POST http://localhost:4001/api/users/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "123invalid",
    "email": "invalid-email",
    "password": "123"
  }'
```
