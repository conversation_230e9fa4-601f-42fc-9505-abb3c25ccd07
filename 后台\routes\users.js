const express = require('express');
const router = express.Router();
const db = require('../config/database');
const { createPasswordHash, generateVerificationCode } = require('../utils/crypto');
const { validateUserRegistration } = require('../middleware/validation');

/**
 * 获取客户端IP地址
 * @param {object} req - Express请求对象
 * @returns {string} IP地址
 */
function getClientIP(req) {
  return req.headers['x-forwarded-for'] || 
         req.connection.remoteAddress || 
         req.socket.remoteAddress ||
         (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
         '127.0.0.1';
}

/**
 * 检查用户名是否已存在
 * @param {string} username - 用户名
 * @returns {boolean} 是否存在
 */
async function checkUsernameExists(username) {
  try {
    const result = await db.query('SELECT user_id FROM users WHERE username = ?', [username]);
    return result.length > 0;
  } catch (error) {
    console.error('检查用户名是否存在时出错:', error);
    throw error;
  }
}

/**
 * 检查邮箱是否已存在
 * @param {string} email - 邮箱
 * @returns {boolean} 是否存在
 */
async function checkEmailExists(email) {
  try {
    const result = await db.query('SELECT user_id FROM users WHERE email = ?', [email]);
    return result.length > 0;
  } catch (error) {
    console.error('检查邮箱是否存在时出错:', error);
    throw error;
  }
}

/**
 * 检查手机号是否已存在
 * @param {string} mobile - 手机号
 * @returns {boolean} 是否存在
 */
async function checkMobileExists(mobile) {
  try {
    if (!mobile) return false;
    const result = await db.query('SELECT user_id FROM users WHERE mobile = ?', [mobile]);
    return result.length > 0;
  } catch (error) {
    console.error('检查手机号是否存在时出错:', error);
    throw error;
  }
}

/**
 * 用户注册接口
 * POST /api/users/register
 */
router.post('/register', validateUserRegistration, async (req, res) => {
  try {
    const {
      username,
      email,
      mobile,
      password,
      nickname,
      bio,
      gender = 0,
      birthday
    } = req.body;

    console.log('📝 收到注册请求:', { username, email, mobile });

    // 检查用户名是否已存在
    const usernameExists = await checkUsernameExists(username);
    if (usernameExists) {
      return res.status(400).json({
        success: false,
        message: '用户名已存在',
        code: 'USERNAME_EXISTS'
      });
    }

    // 检查邮箱是否已存在
    const emailExists = await checkEmailExists(email);
    if (emailExists) {
      return res.status(400).json({
        success: false,
        message: '邮箱已被注册',
        code: 'EMAIL_EXISTS'
      });
    }

    // 检查手机号是否已存在（如果提供了手机号）
    if (mobile) {
      const mobileExists = await checkMobileExists(mobile);
      if (mobileExists) {
        return res.status(400).json({
          success: false,
          message: '手机号已被注册',
          code: 'MOBILE_EXISTS'
        });
      }
    }

    // 生成密码哈希和盐值
    const { salt, hashedPassword } = createPasswordHash(password);

    // 生成验证码
    const verificationCode = generateVerificationCode();
    const verificationExpire = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24小时后过期

    // 获取客户端IP
    const registerIP = getClientIP(req);

    // 准备插入数据
    const userData = {
      username,
      email,
      mobile: mobile || null,
      password_hash: hashedPassword,
      salt,
      nickname: nickname || username,
      bio: bio || null,
      gender: parseInt(gender),
      birthday: birthday || null,
      status: 2, // 未激活状态
      register_ip: registerIP,
      verification_code: verificationCode,
      verification_expire: verificationExpire,
      role: 1 // 普通用户
    };

    // 构建SQL插入语句
    const fields = Object.keys(userData);
    const placeholders = fields.map(() => '?').join(', ');
    const values = Object.values(userData);

    const insertSQL = `
      INSERT INTO users (${fields.join(', ')})
      VALUES (${placeholders})
    `;

    // 执行插入操作
    const result = await db.query(insertSQL, values);

    if (result.affectedRows === 1) {
      // 注册成功，返回用户信息（不包含敏感信息）
      const newUser = {
        user_id: result.insertId,
        username,
        email,
        mobile: mobile || null,
        nickname: nickname || username,
        bio: bio || null,
        gender: parseInt(gender),
        birthday: birthday || null,
        status: 2,
        register_time: new Date().toISOString(),
        role: 1,
        is_verified: 0
      };

      console.log(`✅ 用户注册成功: ${username} (ID: ${result.insertId})`);

      res.status(201).json({
        success: true,
        message: '注册成功，请查收邮件进行账号激活',
        data: {
          user: newUser,
          verification_code: verificationCode // 在实际生产环境中，这个应该通过邮件发送，而不是直接返回
        }
      });
    } else {
      throw new Error('用户注册失败');
    }

  } catch (error) {
    console.error('用户注册错误:', error);

    // 根据错误类型返回不同的错误信息
    if (error.code === 'ER_DUP_ENTRY') {
      return res.status(400).json({
        success: false,
        message: '用户名、邮箱或手机号已存在',
        code: 'DUPLICATE_ENTRY'
      });
    }

    res.status(500).json({
      success: false,
      message: '服务器内部错误，注册失败',
      code: 'INTERNAL_ERROR'
    });
  }
});

/**
 * 获取用户信息接口（示例）
 * GET /api/users/:id
 */
router.get('/:id', async (req, res) => {
  try {
    const userId = req.params.id;
    
    const result = await db.query(`
      SELECT user_id, username, email, mobile, nickname, bio, gender, 
             birthday, status, register_time, last_login_time, role, is_verified
      FROM users 
      WHERE user_id = ?
    `, [userId]);
    
    if (result.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }
    
    res.json({
      success: true,
      data: {
        user: result[0]
      }
    });
    
  } catch (error) {
    console.error('获取用户信息错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

module.exports = router;
