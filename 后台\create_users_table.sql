-- 创建ashuo数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS ashuo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用ashuo数据库
USE ashuo;

-- 创建users表
CREATE TABLE IF NOT EXISTS users (
  user_id bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID，主键',
  username varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名，用于登录和显示',
  email varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '电子邮箱，用于登录和通知',
  mobile varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号码，用于登录和验证',
  password_hash varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '加密后的密码',
  salt varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码加密盐值',
  avatar_url varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像URL',
  nickname varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户昵称',
  bio varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '个人简介',
  gender tinyint(4) DEFAULT '0' COMMENT '性别：0-未知 1-男 2-女',
  birthday date DEFAULT NULL COMMENT '生日',
  status tinyint(4) NOT NULL DEFAULT '1' COMMENT '账号状态：0-禁用 1-正常 2-未激活',
  last_login_time datetime DEFAULT NULL COMMENT '最后登录时间',
  last_login_ip varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后登录IP',
  register_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '注册时间',
  register_ip varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '注册IP',
  update_time datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  is_verified tinyint(4) DEFAULT '0' COMMENT '是否认证：0-未认证 1-已认证',
  verification_code varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱/手机验证码',
  verification_expire datetime DEFAULT NULL COMMENT '验证码过期时间',
  role tinyint(4) DEFAULT '1' COMMENT '用户角色：1-普通用户 2-内容创作者 3-管理员',
  PRIMARY KEY (user_id),
  UNIQUE KEY idx_username (username),
  UNIQUE KEY idx_email (email),
  KEY idx_mobile (mobile),
  KEY idx_status (status),
  KEY idx_register_time (register_time)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 显示创建结果
SHOW TABLES;
DESCRIBE users;
