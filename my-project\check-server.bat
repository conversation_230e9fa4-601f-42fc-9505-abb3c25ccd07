@echo off
echo ================================
echo 知识乐园开发服务器状态检查
echo ================================

REM 检查Node.js版本
echo 1. 检查Node.js版本:
node --version
echo.

REM 检查npm版本
echo 2. 检查npm版本:
npm --version
echo.

REM 检查端口8000状态
echo 3. 检查端口8000状态:
netstat -ano | findstr :8000
if %errorlevel% equ 0 (
    echo 端口8000正在被使用
) else (
    echo 端口8000空闲
)
echo.

REM 测试服务器响应
echo 4. 测试服务器响应:
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:8000' -TimeoutSec 5; Write-Host '服务器响应正常 - 状态码:' $response.StatusCode } catch { Write-Host '服务器无响应或错误:' $_.Exception.Message }"
echo.

REM 检查项目依赖
echo 5. 检查关键依赖:
cd /d "%~dp0"
npm list vue --depth=0 2>nul | findstr vue
npm list pinia --depth=0 2>nul | findstr pinia
npm list vue-router --depth=0 2>nul | findstr vue-router
echo.

echo ================================
echo 检查完成
echo ================================
echo.
echo 如果服务器无响应，请运行 restart-server.bat
echo 如果依赖缺失，请运行 npm install
echo.
pause
