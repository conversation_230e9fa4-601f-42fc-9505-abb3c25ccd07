<template>
  <div id="app">
    <header class="header">
      <nav class="nav">
        <router-link to="/" class="logo">我的网站</router-link>
        <div class="nav-links">
          <router-link to="/" class="nav-link">首页</router-link>
        </div>
      </nav>
    </header>

    <main class="main">
      <router-view />
    </main>

    <footer class="footer">
      <p>&copy; 2025 我的网站. 基于 Vue 3 + TypeScript + Vite 构建</p>
    </footer>
  </div>
</template>

<script setup lang="ts">
// App.vue - 主应用组件
</script>

<style scoped>
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background-color: #2c3e50;
  color: white;
  padding: 1rem 0;
}

.nav {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
}

.logo {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
  text-decoration: none;
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-link {
  color: white;
  text-decoration: none;
  transition: color 0.3s ease;
}

.nav-link:hover,
.nav-link.router-link-active {
  color: #3498db;
}

.main {
  flex: 1;
}

.footer {
  background-color: #34495e;
  color: white;
  text-align: center;
  padding: 2rem 0;
  margin-top: auto;
}

.footer p {
  margin: 0;
}
</style>
