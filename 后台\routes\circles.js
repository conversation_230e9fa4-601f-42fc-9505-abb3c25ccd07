const express = require('express');
const router = express.Router();
const db = require('../config/database');
const { validateCircleListParams } = require('../middleware/validation');

/**
 * 构建圈子列表查询SQL
 * @param {object} params - 查询参数
 * @returns {object} 包含SQL语句和参数的对象
 */
function buildCircleListQuery(params) {
  let sql = `
    SELECT 
      id,
      category_id,
      category_name,
      type,
      name,
      cover,
      introduction,
      content_description,
      price,
      annual_fee_type,
      create_time,
      update_time,
      status,
      author_id,
      author_name,
      member_count,
      post_count
    FROM circle
    WHERE 1=1
  `;
  
  const queryParams = [];
  
  // 添加筛选条件
  if (params.category_id !== undefined) {
    sql += ' AND category_id = ?';
    queryParams.push(params.category_id);
  }
  
  if (params.type !== undefined) {
    sql += ' AND type = ?';
    queryParams.push(params.type);
  }
  
  if (params.status !== undefined) {
    sql += ' AND status = ?';
    queryParams.push(params.status);
  }
  
  if (params.author_id !== undefined) {
    sql += ' AND author_id = ?';
    queryParams.push(params.author_id);
  }
  
  if (params.keyword) {
    sql += ' AND (name LIKE ? OR introduction LIKE ? OR content_description LIKE ?)';
    const keywordPattern = `%${params.keyword}%`;
    queryParams.push(keywordPattern, keywordPattern, keywordPattern);
  }
  
  // 添加排序
  sql += ` ORDER BY ${params.sort_by} ${params.sort_order.toUpperCase()}`;
  
  // 添加分页
  const offset = (params.page - 1) * params.pageSize;
  sql += ' LIMIT ? OFFSET ?';
  queryParams.push(params.pageSize, offset);
  
  return { sql, queryParams };
}

/**
 * 构建圈子总数查询SQL
 * @param {object} params - 查询参数
 * @returns {object} 包含SQL语句和参数的对象
 */
function buildCircleCountQuery(params) {
  let sql = 'SELECT COUNT(*) as total FROM circle WHERE 1=1';
  const queryParams = [];
  
  // 添加筛选条件（与列表查询相同的条件）
  if (params.category_id !== undefined) {
    sql += ' AND category_id = ?';
    queryParams.push(params.category_id);
  }
  
  if (params.type !== undefined) {
    sql += ' AND type = ?';
    queryParams.push(params.type);
  }
  
  if (params.status !== undefined) {
    sql += ' AND status = ?';
    queryParams.push(params.status);
  }
  
  if (params.author_id !== undefined) {
    sql += ' AND author_id = ?';
    queryParams.push(params.author_id);
  }
  
  if (params.keyword) {
    sql += ' AND (name LIKE ? OR introduction LIKE ? OR content_description LIKE ?)';
    const keywordPattern = `%${params.keyword}%`;
    queryParams.push(keywordPattern, keywordPattern, keywordPattern);
  }
  
  return { sql, queryParams };
}

/**
 * 获取圈子列表接口
 * POST /api/circles/list
 */
router.post('/list', validateCircleListParams, async (req, res) => {
  try {
    const params = req.validatedParams;
    
    console.log('📝 收到圈子列表查询请求:', params);
    
    // 构建查询SQL
    const listQuery = buildCircleListQuery(params);
    const countQuery = buildCircleCountQuery(params);
    
    // 并行执行列表查询和总数查询
    const [listResult, countResult] = await Promise.all([
      db.query(listQuery.sql, listQuery.queryParams),
      db.query(countQuery.sql, countQuery.queryParams)
    ]);
    
    const circles = listResult;
    const total = countResult[0].total;
    
    // 计算分页信息
    const totalPages = Math.ceil(total / params.pageSize);
    const hasNextPage = params.page < totalPages;
    const hasPrevPage = params.page > 1;
    
    // 格式化返回数据
    const formattedCircles = circles.map(circle => ({
      id: circle.id,
      category_id: circle.category_id,
      category_name: circle.category_name,
      type: circle.type,
      type_name: circle.type === 1 ? '公开' : '私密',
      name: circle.name,
      cover: circle.cover,
      introduction: circle.introduction,
      content_description: circle.content_description,
      price: parseFloat(circle.price),
      annual_fee_type: circle.annual_fee_type,
      annual_fee_type_name: getAnnualFeeTypeName(circle.annual_fee_type),
      create_time: circle.create_time,
      update_time: circle.update_time,
      status: circle.status,
      status_name: getStatusName(circle.status),
      author_id: circle.author_id,
      author_name: circle.author_name,
      member_count: circle.member_count || 0,
      post_count: circle.post_count || 0
    }));
    
    console.log(`✅ 圈子列表查询成功: 返回${circles.length}条记录，总计${total}条`);
    
    res.json({
      success: true,
      message: '获取圈子列表成功',
      data: {
        circles: formattedCircles,
        pagination: {
          current_page: params.page,
          page_size: params.pageSize,
          total_count: total,
          total_pages: totalPages,
          has_next_page: hasNextPage,
          has_prev_page: hasPrevPage
        },
        filters: {
          category_id: params.category_id,
          type: params.type,
          status: params.status,
          author_id: params.author_id,
          keyword: params.keyword,
          sort_by: params.sort_by,
          sort_order: params.sort_order
        }
      }
    });
    
  } catch (error) {
    console.error('圈子列表查询错误:', error);
    
    res.status(500).json({
      success: false,
      message: '服务器内部错误，获取圈子列表失败',
      code: 'INTERNAL_ERROR'
    });
  }
});

/**
 * 获取年费类型名称
 * @param {number} type - 年费类型
 * @returns {string} 类型名称
 */
function getAnnualFeeTypeName(type) {
  const typeMap = {
    1: '免费',
    2: '年费',
    3: '月费',
    4: '一次性付费'
  };
  return typeMap[type] || '未知';
}

/**
 * 获取状态名称
 * @param {number} status - 状态值
 * @returns {string} 状态名称
 */
function getStatusName(status) {
  const statusMap = {
    0: '禁用',
    1: '正常',
    2: '审核中',
    3: '已删除'
  };
  return statusMap[status] || '未知';
}

/**
 * 获取单个圈子详情接口
 * GET /api/circles/:id
 */
router.get('/:id', async (req, res) => {
  try {
    const circleId = parseInt(req.params.id);
    
    if (isNaN(circleId) || circleId < 1) {
      return res.status(400).json({
        success: false,
        message: '圈子ID必须是大于0的整数'
      });
    }
    
    const result = await db.query(`
      SELECT 
        id, category_id, category_name, type, name, cover, introduction, 
        content_description, price, annual_fee_type, create_time, update_time, 
        status, author_id, author_name, member_count, post_count
      FROM circle 
      WHERE id = ?
    `, [circleId]);
    
    if (result.length === 0) {
      return res.status(404).json({
        success: false,
        message: '圈子不存在'
      });
    }
    
    const circle = result[0];
    const formattedCircle = {
      id: circle.id,
      category_id: circle.category_id,
      category_name: circle.category_name,
      type: circle.type,
      type_name: circle.type === 1 ? '公开' : '私密',
      name: circle.name,
      cover: circle.cover,
      introduction: circle.introduction,
      content_description: circle.content_description,
      price: parseFloat(circle.price),
      annual_fee_type: circle.annual_fee_type,
      annual_fee_type_name: getAnnualFeeTypeName(circle.annual_fee_type),
      create_time: circle.create_time,
      update_time: circle.update_time,
      status: circle.status,
      status_name: getStatusName(circle.status),
      author_id: circle.author_id,
      author_name: circle.author_name,
      member_count: circle.member_count || 0,
      post_count: circle.post_count || 0
    };
    
    res.json({
      success: true,
      data: {
        circle: formattedCircle
      }
    });
    
  } catch (error) {
    console.error('获取圈子详情错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
});

module.exports = router;
