# lodash.isstring v4.0.1

The [lodash](https://lodash.com/) method `_.isString` exported as a [Node.js](https://nodejs.org/) module.

## Installation

Using npm:
```bash
$ {sudo -H} npm i -g npm
$ npm i --save lodash.isstring
```

In Node.js:
```js
var isString = require('lodash.isstring');
```

See the [documentation](https://lodash.com/docs#isString) or [package source](https://github.com/lodash/lodash/blob/4.0.1-npm-packages/lodash.isstring) for more details.
